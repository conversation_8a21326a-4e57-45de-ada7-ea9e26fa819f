import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score
from sklearn.preprocessing import StandardScaler
from scipy import stats
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

# 数据路径
DEFAULT_PATHS = { 
    "points_jrc_gap": r"D:\STNS\STNS\Global_validation\参数15\points_jrc_gap.csv",   # 提供 count_clean
    "points_jrc": r"D:\STNS\STNS\Global_validation\参数15\points_jrc.csv",            # 提供 gaps_rate
    "terrain": r"D:\STNS\STNS\Global_validation\参数15\terrain.csv",                 # 提供 slope
    "contribution": r"D:\STNS\STNS\Global_validation\参数15\contribution_analysis.csv", # 提供 contrib_*
    "validation": r"D:\STNS\STNS\Global_validation\参数15\validation.csv",            # 提供 F1_spatial/F1_temporal 等绝对指标
}

# 兼容可能的列名别名
ALIASES: Dict[str, List[str]] = {
    "id": ["id", "ID", "point_id", "pid"],
    "type": ["type", "Type", "TYPE", "point_type"],
    "gaps_rate": ["gaps_rate", "gap_rate", "gaps", "gapsrate"],
    "count_clean": ["count_clean", "clean_count", "n_clean", "clean_samples"],
    "slope": ["slope", "Slope", "slope_deg", "slope_degree", "slope_degrees"],
    "f1_rec": ["f1_rec", "F1_rec", "f1_reconstruction", "F1_reconstruction"],
    "acc_rec": ["acc_rec", "Acc_rec", "acc_reconstruction", "Acc_reconstruction"],
}

def _try_read_csv(path: str) -> pd.DataFrame:
    """尝试多种编码读取CSV文件"""
    encs = ["utf-8-sig", "utf-8", "gbk", "cp936", None]
    engines = [None, "python"]
    last = None
    for enc in encs:
        for eng in engines:
            try:
                return pd.read_csv(path, encoding=enc, engine=eng)
            except Exception as e:
                last = e
    raise RuntimeError(f"读取CSV失败: {path}; 最后错误: {last}")

def _norm_cols(df: pd.DataFrame) -> pd.DataFrame:
    """标准化列名"""
    out = df.copy()
    out.columns = [str(c).strip().lower().replace(" ", "_") for c in out.columns]
    return out

def _find_col(df: pd.DataFrame, alias: List[str]) -> str:
    """根据别名查找列"""
    cols = set(df.columns)
    for a in alias:
        k = str(a).strip().lower().replace(" ", "_")
        if k in cols:
            return k
    raise KeyError(f"找不到列，尝试别名: {alias}; 现有列: {list(df.columns)}")

def _select_rename(df: pd.DataFrame, needs: Dict[str, List[str]]) -> pd.DataFrame:
    """选择并重命名列"""
    d = _norm_cols(df)
    mapping = {k: _find_col(d, v) for k, v in needs.items()}
    sub = d[list(mapping.values())].copy().rename(columns={v: k for k, v in mapping.items()})
    return sub

def _to_num(df: pd.DataFrame, cols: List[str]) -> pd.DataFrame:
    """转换为数值类型"""
    x = df.copy()
    for c in cols:
        x[c] = pd.to_numeric(x[c], errors="coerce")
    return x

def _dedup(df: pd.DataFrame, keys: List[str]) -> pd.DataFrame:
    """去重，数值列取平均，其他列取第一个"""
    if not df.duplicated(keys).any():
        return df
    num_cols = [c for c in df.columns if c not in keys and pd.api.types.is_numeric_dtype(df[c])]
    oth_cols = [c for c in df.columns if c not in keys + num_cols]
    agg = {**{c: "mean" for c in num_cols}, **{c: "first" for c in oth_cols}}
    return df.groupby(keys, as_index=False).agg(agg)

def load_and_merge_data(paths: Dict[str, str]) -> pd.DataFrame:
    """加载并合并所有数据"""
    # 读取各个文件
    jrc = _try_read_csv(paths["points_jrc"])             # gaps_rate
    gap = _try_read_csv(paths["points_jrc_gap"])         # count_clean
    ter = _try_read_csv(paths["terrain"])                # slope
    val = _try_read_csv(paths["validation"])             # F1_rec, acc_rec
    
    # 选择和重命名列
    jrc = _select_rename(jrc, {"id": ALIASES["id"], "type": ALIASES["type"], "gaps_rate": ALIASES["gaps_rate"]})
    gap = _select_rename(gap, {"id": ALIASES["id"], "type": ALIASES["type"], "count_clean": ALIASES["count_clean"]})
    ter = _select_rename(ter, {"id": ALIASES["id"], "type": ALIASES["type"], "slope": ALIASES["slope"]})
    val = _select_rename(val, {"id": ALIASES["id"], "type": ALIASES["type"], 
                               "f1_rec": ALIASES["f1_rec"], "acc_rec": ALIASES["acc_rec"]})
    
    # 转换为数值类型
    jrc = _to_num(jrc, ["gaps_rate"])
    gap = _to_num(gap, ["count_clean"])
    ter = _to_num(ter, ["slope"])
    val = _to_num(val, ["f1_rec", "acc_rec"])
    
    # 去重
    keys = ["id", "type"]
    jrc = _dedup(jrc, keys)
    gap = _dedup(gap, keys)
    ter = _dedup(ter, keys)
    val = _dedup(val, keys)
    
    # 合并数据
    df = val.merge(jrc, on=keys, how="inner")\
           .merge(gap, on=keys, how="inner")\
           .merge(ter, on=keys, how="inner")
    
    # 删除缺失值
    df = df.dropna(subset=["f1_rec", "acc_rec", "gaps_rate", "count_clean", "slope"]).reset_index(drop=True)
    
    return df

def perform_regression_analysis(df: pd.DataFrame) -> Dict:
    """执行回归分析"""
    # 准备数据
    X_vars = ["slope", "count_clean", "gaps_rate"]
    y_vars = ["f1_rec", "acc_rec"]
    
    results = {}
    
    for y_var in y_vars:
        print(f"\n{'='*50}")
        print(f"回归分析: {y_var}")
        print(f"{'='*50}")
        
        # 准备数据
        X = df[X_vars].values
        y = df[y_var].values
        
        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # 线性回归
        model = LinearRegression()
        model.fit(X_scaled, y)
        y_pred = model.predict(X_scaled)
        
        # 计算R²
        r2 = r2_score(y, y_pred)
        
        # 计算相关系数和p值
        correlations = {}
        for i, var in enumerate(X_vars):
            corr, p_val = stats.pearsonr(df[var], df[y_var])
            correlations[var] = {"correlation": corr, "p_value": p_val}
        
        results[y_var] = {
            "model": model,
            "scaler": scaler,
            "r2": r2,
            "correlations": correlations,
            "coefficients": dict(zip(X_vars, model.coef_)),
            "intercept": model.intercept_
        }
        
        # 打印结果
        print(f"R² = {r2:.4f}")
        print(f"截距 = {model.intercept_:.4f}")
        print("\n回归系数 (标准化后):")
        for var, coef in zip(X_vars, model.coef_):
            print(f"  {var}: {coef:.4f}")
        
        print("\n相关系数和显著性:")
        for var, stats_dict in correlations.items():
            significance = "***" if stats_dict["p_value"] < 0.001 else \
                          "**" if stats_dict["p_value"] < 0.01 else \
                          "*" if stats_dict["p_value"] < 0.05 else ""
            print(f"  {var}: r={stats_dict['correlation']:.4f}, p={stats_dict['p_value']:.4f} {significance}")
    
    return results

def analyze_poor_samples(df: pd.DataFrame, percentile: float = 25) -> pd.DataFrame:
    """分析较差样本的特点"""
    print(f"\n{'='*50}")
    print(f"分析F1_rec分位数{percentile}%以下的较差样本特点")
    print(f"{'='*50}")
    
    # 计算F1_rec的分位数阈值
    threshold = np.percentile(df["f1_rec"], percentile)
    print(f"F1_rec {percentile}%分位数阈值: {threshold:.4f}")
    
    # 分离较差样本和其他样本
    poor_samples = df[df["f1_rec"] <= threshold].copy()
    good_samples = df[df["f1_rec"] > threshold].copy()
    
    print(f"较差样本数量: {len(poor_samples)} ({len(poor_samples)/len(df)*100:.1f}%)")
    print(f"其他样本数量: {len(good_samples)} ({len(good_samples)/len(df)*100:.1f}%)")
    
    # 分析各变量的统计特征
    analysis_vars = ["f1_rec", "acc_rec", "slope", "count_clean", "gaps_rate"]
    
    print(f"\n较差样本 vs 其他样本的统计对比:")
    print("-" * 80)
    
    comparison_stats = []
    for var in analysis_vars:
        poor_stats = poor_samples[var].describe()
        good_stats = good_samples[var].describe()
        
        # t检验
        t_stat, p_val = stats.ttest_ind(poor_samples[var], good_samples[var])
        
        comparison_stats.append({
            "变量": var,
            "较差样本_均值": poor_stats["mean"],
            "较差样本_标准差": poor_stats["std"],
            "其他样本_均值": good_stats["mean"],
            "其他样本_标准差": good_stats["std"],
            "t统计量": t_stat,
            "p值": p_val
        })
        
        significance = "***" if p_val < 0.001 else "**" if p_val < 0.01 else "*" if p_val < 0.05 else ""
        print(f"{var:12} | 较差: {poor_stats['mean']:.4f}±{poor_stats['std']:.4f} | "
              f"其他: {good_stats['mean']:.4f}±{good_stats['std']:.4f} | "
              f"t={t_stat:.3f}, p={p_val:.4f} {significance}")
    
    # 分析type分布
    print(f"\n类型(type)分布对比:")
    print("-" * 40)
    poor_type_dist = poor_samples["type"].value_counts(normalize=True)
    good_type_dist = good_samples["type"].value_counts(normalize=True)
    
    all_types = set(poor_type_dist.index) | set(good_type_dist.index)
    for t in sorted(all_types):
        poor_pct = poor_type_dist.get(t, 0) * 100
        good_pct = good_type_dist.get(t, 0) * 100
        print(f"类型 {t}: 较差样本 {poor_pct:.1f}% | 其他样本 {good_pct:.1f}%")
    
    return pd.DataFrame(comparison_stats)

def create_visualizations(df: pd.DataFrame, results: Dict, save_dir: str = "Code"):
    """创建可视化图表"""
    # 设置中文字体
    plt.rcParams["font.sans-serif"] = ["SimHei", "Microsoft YaHei", "Arial Unicode MS", "DejaVu Sans"]
    plt.rcParams["axes.unicode_minus"] = False

    # 创建保存目录
    os.makedirs(save_dir, exist_ok=True)

    # 1. 相关性热力图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))

    # 计算相关矩阵
    corr_vars = ["f1_rec", "acc_rec", "slope", "count_clean", "gaps_rate"]
    corr_matrix = df[corr_vars].corr()

    # 热力图
    sns.heatmap(corr_matrix, annot=True, cmap="RdBu_r", center=0,
                square=True, ax=axes[0,0], cbar_kws={"shrink": 0.8})
    axes[0,0].set_title("变量相关性热力图")

    # 2. 散点图矩阵 - F1_rec vs 各因子
    X_vars = ["slope", "count_clean", "gaps_rate"]
    for i, var in enumerate(X_vars):
        row = (i + 1) // 2
        col = (i + 1) % 2

        axes[row, col].scatter(df[var], df["f1_rec"], alpha=0.6, s=20)
        axes[row, col].set_xlabel(var)
        axes[row, col].set_ylabel("F1_rec")
        axes[row, col].set_title(f"F1_rec vs {var}")

        # 添加回归线
        z = np.polyfit(df[var], df["f1_rec"], 1)
        p = np.poly1d(z)
        axes[row, col].plot(df[var], p(df[var]), "r--", alpha=0.8)

        # 添加相关系数
        corr = results["f1_rec"]["correlations"][var]["correlation"]
        axes[row, col].text(0.05, 0.95, f'r = {corr:.3f}',
                           transform=axes[row, col].transAxes,
                           bbox=dict(boxstyle="round", facecolor="white", alpha=0.8))

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, "精度回归分析_相关性分析.png"), dpi=300, bbox_inches='tight')
    plt.close()

    # 3. 较差样本分布分析
    fig, axes = plt.subplots(2, 2, figsize=(16, 10))

    # F1_rec分位数25%阈值
    threshold = np.percentile(df["f1_rec"], 25)
    poor_mask = df["f1_rec"] <= threshold

    # 各变量的分布对比
    vars_to_plot = ["slope", "count_clean", "gaps_rate"]
    for i, var in enumerate(vars_to_plot):
        row = i // 2
        col = i % 2

        # 箱线图
        data_to_plot = [df[~poor_mask][var].dropna(), df[poor_mask][var].dropna()]
        axes[row, col].boxplot(data_to_plot, labels=["其他样本", "较差样本"])
        axes[row, col].set_title(f"{var} 分布对比")
        axes[row, col].set_ylabel(var)
        axes[row, col].grid(True, alpha=0.3)

    # F1_rec分布直方图
    axes[1, 1].hist(df["f1_rec"], bins=30, alpha=0.7, color="skyblue", edgecolor="black")
    axes[1, 1].axvline(threshold, color="red", linestyle="--", linewidth=2,
                       label=f"25%分位数: {threshold:.3f}")
    axes[1, 1].set_xlabel("F1_rec")
    axes[1, 1].set_ylabel("频数")
    axes[1, 1].set_title("F1_rec分布")
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, "精度回归分析_较差样本分析.png"), dpi=300, bbox_inches='tight')
    plt.close()

def main(paths: Dict[str, str] = DEFAULT_PATHS, save_dir: str = "Code"):
    """主函数"""
    print("开始精度回归分析...")

    # 检查文件是否存在
    for name, path in paths.items():
        if not os.path.exists(path):
            raise FileNotFoundError(f"文件不存在: {name} -> {path}")

    # 加载数据
    print("正在加载和合并数据...")
    df = load_and_merge_data(paths)
    print(f"合并后数据形状: {df.shape}")
    print(f"数据概览:")
    print(df.describe())

    # 执行回归分析
    print("\n开始回归分析...")
    results = perform_regression_analysis(df)

    # 分析较差样本
    print("\n开始分析较差样本...")
    comparison_stats = analyze_poor_samples(df, percentile=25)

    # 创建可视化
    print("\n创建可视化图表...")
    create_visualizations(df, results, save_dir)

    # 保存结果
    output_file = os.path.join(save_dir, "精度回归分析_结果.csv")
    comparison_stats.to_csv(output_file, index=False, encoding="utf-8-sig")
    print(f"\n分析结果已保存到: {output_file}")

    return df, results, comparison_stats

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="分析slope、count_clean、gaps_rate与精度的回归关系")
    parser.add_argument("--jrc_gap", default=DEFAULT_PATHS["points_jrc_gap"],
                       help="points_jrc_gap.csv 路径")
    parser.add_argument("--jrc", default=DEFAULT_PATHS["points_jrc"],
                       help="points_jrc.csv 路径")
    parser.add_argument("--terrain", default=DEFAULT_PATHS["terrain"],
                       help="terrain.csv 路径")
    parser.add_argument("--validation", default=DEFAULT_PATHS["validation"],
                       help="validation.csv 路径")
    parser.add_argument("--save_dir", default="Code",
                       help="结果保存目录")

    args = parser.parse_args()

    paths = {
        "points_jrc_gap": args.jrc_gap,
        "points_jrc": args.jrc,
        "terrain": args.terrain,
        "validation": args.validation,
        "contribution": DEFAULT_PATHS["contribution"]  # 虽然不用，但保持结构完整
    }

    df, results, comparison_stats = main(paths, args.save_dir)
